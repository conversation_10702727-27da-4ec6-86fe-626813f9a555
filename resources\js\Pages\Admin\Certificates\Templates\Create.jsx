import React, { useState, useEffect } from 'react';
import { Head, useForm } from '@inertiajs/react';
import AdminAuthenticatedLayout from '@/Layouts/AdminAuthenticatedLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { toast } from 'sonner';

export default function Create({ page_title, types }) {
  const [showStudentQR, setShowStudentQR] = useState(false);
  const [showStaffQR, setShowStaffQR] = useState(false);

  const { data, setData, post, processing, errors } = useForm({
    name: '',
    type_id: '',
    layout: '',
    height: '',
    width: '',
    status: 1,
    qr_code_student: ['admission_no'],
    qr_code_staff: ['staff_id'],
    type_role_id: '',
    user_photo_style: 1,
    user_image_size: '100',
    qr_image_size: '100',
    content: '',
    background_image: null,
    signature_image: null,
    logo_image: null,
  });

  // Handle certificate type change
  const handleTypeChange = (value) => {
    setData('type_id', value);
    const selectedType = types.find(type => type.id == value);
    if (selectedType) {
      if (selectedType.usertype === 'student') {
        setData('type_role_id', '2');
        setShowStudentQR(true);
        setShowStaffQR(false);
      } else {
        setData('type_role_id', '1');
        setShowStudentQR(false);
        setShowStaffQR(true);
      }
    }
  };

  // Handle layout change
  const handleLayoutChange = (value) => {
    setData('layout', value);
    if (value === '1') { // A4 Portrait
      setData(prev => ({ ...prev, width: '210', height: '297' }));
    } else if (value === '2') { // A4 Landscape
      setData(prev => ({ ...prev, width: '297', height: '210' }));
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    const formData = new FormData();
    Object.keys(data).forEach(key => {
      if (key === 'background_image' || key === 'signature_image' || key === 'logo_image') {
        if (data[key]) {
          formData.append(key, data[key]);
        }
      } else {
        formData.append(key, data[key]);
      }
    });

    post(route('admin.certificate.templates.store'), {
      data: formData,
      forceFormData: true,
      onSuccess: () => {
        toast.success('Certificate template created successfully!');
      },
      onError: (errors) => {
        console.error('Validation errors:', errors);
        Object.keys(errors).forEach(key => {
          toast.error(errors[key]);
        });
      }
    });
  };

  return (
    <AdminAuthenticatedLayout>
      <Head title={page_title} />
      
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">{page_title}</h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Create New Certificate Template</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Template Name */}
              <div>
                <Label htmlFor="name">
                  Template Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  type="text"
                  value={data.name}
                  onChange={e => setData('name', e.target.value)}
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
              </div>

              {/* Certificate Type */}
              <div>
                <Label>
                  Certificate Type <span className="text-red-500">*</span>
                </Label>
                <Select onValueChange={handleTypeChange}>
                  <SelectTrigger className={errors.type_id ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select certificate type" />
                  </SelectTrigger>
                  <SelectContent>
                    {types.map(type => (
                      <SelectItem key={type.id} value={type.id.toString()}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.type_id && <p className="text-red-500 text-sm mt-1">{errors.type_id}</p>}
              </div>

              {/* Layout */}
              <div>
                <Label>
                  Layout <span className="text-red-500">*</span>
                </Label>
                <Select onValueChange={handleLayoutChange}>
                  <SelectTrigger className={errors.layout ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select layout" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">A4 Portrait</SelectItem>
                    <SelectItem value="2">A4 Landscape</SelectItem>
                    <SelectItem value="3">Custom</SelectItem>
                  </SelectContent>
                </Select>
                {errors.layout && <p className="text-red-500 text-sm mt-1">{errors.layout}</p>}
              </div>

              {/* Dimensions */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="width">
                    Width (mm) <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="width"
                    type="number"
                    value={data.width}
                    onChange={e => setData('width', e.target.value)}
                    className={errors.width ? 'border-red-500' : ''}
                  />
                  {errors.width && <p className="text-red-500 text-sm mt-1">{errors.width}</p>}
                </div>
                <div>
                  <Label htmlFor="height">
                    Height (mm) <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="height"
                    type="number"
                    value={data.height}
                    onChange={e => setData('height', e.target.value)}
                    className={errors.height ? 'border-red-500' : ''}
                  />
                  {errors.height && <p className="text-red-500 text-sm mt-1">{errors.height}</p>}
                </div>
              </div>

              {/* User Photo Style */}
              <div>
                <Label>User Photo Style</Label>
                <Select onValueChange={value => setData('user_photo_style', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select photo style" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">No Photo</SelectItem>
                    <SelectItem value="1">Circle</SelectItem>
                    <SelectItem value="2">Square</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* User Image Size */}
              {data.user_photo_style !== '0' && (
                <div>
                  <Label htmlFor="user_image_size">User Image Size (px)</Label>
                  <Input
                    id="user_image_size"
                    type="number"
                    value={data.user_image_size}
                    onChange={e => setData('user_image_size', e.target.value)}
                  />
                </div>
              )}

              {/* QR Code Size */}
              <div>
                <Label htmlFor="qr_image_size">QR Code Size (px)</Label>
                <Input
                  id="qr_image_size"
                  type="number"
                  value={data.qr_image_size}
                  onChange={e => setData('qr_image_size', e.target.value)}
                  min="100"
                />
              </div>

              {/* QR Code Sections */}
              {showStudentQR && (
                <div className="mt-6">
                  <Label>
                    QR Code Text <span className="text-red-500">*</span>
                  </Label>
                  <ScrollArea className="h-[200px] w-full border rounded-md p-4">
                    <div className="space-y-2">
                      {['admission_no', 'roll_no', 'date_of_birth', 'certificate_number', 'link'].map(option => (
                        <label key={option} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={data.qr_code_student.includes(option)}
                            onChange={e => {
                              const newValue = e.target.checked
                                ? [...data.qr_code_student, option]
                                : data.qr_code_student.filter(item => item !== option);
                              setData('qr_code_student', newValue);
                            }}
                            className="rounded border-gray-300"
                          />
                          <span className="capitalize">{option.replace(/_/g, ' ')}</span>
                        </label>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              )}

              {showStaffQR && (
                <div className="mt-6">
                  <Label>
                    QR Code Text <span className="text-red-500">*</span>
                  </Label>
                  <ScrollArea className="h-[200px] w-full border rounded-md p-4">
                    <div className="space-y-2">
                      {['staff_id', 'joining_date', 'certificate_number', 'link'].map(option => (
                        <label key={option} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={data.qr_code_staff.includes(option)}
                            onChange={e => {
                              const newValue = e.target.checked
                                ? [...data.qr_code_staff, option]
                                : data.qr_code_staff.filter(item => item !== option);
                              setData('qr_code_staff', newValue);
                            }}
                            className="rounded border-gray-300"
                          />
                          <span className="capitalize">{option.replace(/_/g, ' ')}</span>
                        </label>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              )}

              {/* File Uploads */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="background_image">Background Image</Label>
                  <Input
                    id="background_image"
                    type="file"
                    accept="image/*"
                    onChange={e => setData('background_image', e.target.files[0])}
                  />
                </div>
                <div>
                  <Label htmlFor="logo_image">Logo Image</Label>
                  <Input
                    id="logo_image"
                    type="file"
                    accept="image/*"
                    onChange={e => setData('logo_image', e.target.files[0])}
                  />
                </div>
                <div>
                  <Label htmlFor="signature_image">Signature Image</Label>
                  <Input
                    id="signature_image"
                    type="file"
                    accept="image/*"
                    onChange={e => setData('signature_image', e.target.files[0])}
                  />
                </div>
              </div>

              {/* Content */}
              <div>
                <Label htmlFor="content">
                  Content <span className="text-red-500">*</span>
                </Label>
                <Textarea
                  id="content"
                  value={data.content}
                  onChange={e => setData('content', e.target.value)}
                  rows={4}
                  className={errors.content ? 'border-red-500' : ''}
                  placeholder="Enter certificate content..."
                />
                {errors.content && <p className="text-red-500 text-sm mt-1">{errors.content}</p>}
              </div>

              {/* Submit Button */}
              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => window.history.back()}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={processing}>
                  {processing ? 'Creating...' : 'Create Template'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </AdminAuthenticatedLayout>
  );
}
