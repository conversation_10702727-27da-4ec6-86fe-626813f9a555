import React from 'react';
import { Link, router } from '@inertiajs/react';

export default function Index({ templates }) {
  return (
    <div>
      <h1>Certificate Templates</h1>

      <Link href={route('admin.certificate.templates.create')}>+ New Template</Link>

      <ul className="mt-4">
        {templates.map(template => (
          <li key={template.id} className="mb-2">
            <strong>{template.name}</strong> ({template.type?.name})
            <div className="space-x-2 mt-1">
              <Link href={route('admin.certificate.templates.edit', template.id)}>Edit</Link>
              <Link href={route('admin.certificate.templates.design', template.id)}>Design</Link>
              <button
                onClick={() => {
                  if (confirm('Are you sure?')) {
                    router.delete(route('admin.certificate.templates.destroy', template.id));
                  }
                }}
              >
                Delete
              </button>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
}
