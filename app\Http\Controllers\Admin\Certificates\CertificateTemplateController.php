<?php

namespace App\Http\Controllers\Admin\Certificates;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\CertificateType;
use App\Models\CertificateTemplate;
use App\Models\CertificateTemplateDesign;

class CertificateTemplateController extends Controller
{
    /** Show listing */
    public function index()
    {
        $templates = CertificateTemplate::with('type')->get();
        return Inertia::render('Admin/Certificates/Templates/Index', compact('templates'));
    }

    /** Show form for create */
    public function create()
    {
        $types = CertificateType::all();
        return Inertia::render('Admin/Certificates/Templates/CreateEdit', compact('types'));
    }

    /** Show form for edit */
    public function edit($id)
    {
        $template = CertificateTemplate::findOrFail($id);
        $types    = CertificateType::all();
        return Inertia::render('Admin/Certificates/Templates/CreateEdit', compact('template', 'types'));
    }

    /** Store or update */
    public function store(Request $request)
    {
        $data = $request->validate([
            'id'                   => 'nullable|exists:certificate_templates,id',
            'name'                 => 'required|string',
            'certificate_type_id'  => 'required|exists:certificate_types,id',
            'layout'               => 'required',
            'content'              => 'required|string',
            // add your QR, image fields as needed
        ]);

        CertificateTemplate::updateOrCreate(
            ['id' => $data['id']],
            $data
        );

        return redirect()->route('admin.certificate.templates.index');
    }

    /** Delete template */
    public function destroy($id)
    {
        $template = CertificateTemplate::with('design')->findOrFail($id);
        if ($template->design) {
            $template->design->delete();
        }
        $template->delete();

        return back()->with('success', 'Template deleted.');
    }

    /** Show design editor */
    public function design($id)
    {
        $template = CertificateTemplate::findOrFail($id);
        return Inertia::render('Admin/Certificates/Templates/Design', compact('template'));
    }

    /** Save design JSON */
    public function updateDesign(Request $request)
    {
        $data = $request->validate([
            'template_id'    => 'required|exists:certificate_templates,id',
            'design_content' => 'required|string',
        ]);

        $design = CertificateTemplateDesign::updateOrCreate(
            ['certificate_template_id' => $data['template_id']],
            ['design_content'         => $data['design_content']]
        );

        return response()->json(['status' => 'success']);
    }
}
