import React from 'react';
import { useForm } from '@inertiajs/react';

export default function CreateEdit({ template = null, types }) {
  const { data, setData, post, processing, errors } = useForm({
    id: template?.id || '',
    name: template?.name || '',
    certificate_type_id: template?.certificate_type_id || '',
    layout: template?.layout || '1',
    content: template?.content || '',
  });

  const handleSubmit = e => {
    e.preventDefault();
    post(route('admin.certificate.templates.store'));
  };

  return (
    <form onSubmit={handleSubmit}>
      <h1>{template ? 'Edit' : 'Create'} Template</h1>

      <div>
        <label>Name</label>
        <input value={data.name} onChange={e => setData('name', e.target.value)} />
        {errors.name && <div>{errors.name}</div>}
      </div>

      <div>
        <label>Type</label>
        <select value={data.certificate_type_id} onChange={e => setData('certificate_type_id', e.target.value)}>
          <option value="">Select Type</option>
          {types.map(type => (
            <option key={type.id} value={type.id}>
              {type.name}
            </option>
          ))}
        </select>
        {errors.certificate_type_id && <div>{errors.certificate_type_id}</div>}
      </div>

      <div>
        <label>Layout</label>
        <select value={data.layout} onChange={e => setData('layout', e.target.value)}>
          <option value="1">A4 Portrait</option>
          <option value="2">A4 Landscape</option>
          <option value="3">Custom</option>
        </select>
      </div>

      <div>
        <label>Content</label>
        <textarea value={data.content} onChange={e => setData('content', e.target.value)} />
        {errors.content && <div>{errors.content}</div>}
      </div>

      <button type="submit" disabled={processing}>
        {template ? 'Update' : 'Create'}
      </button>
    </form>
  );
}
